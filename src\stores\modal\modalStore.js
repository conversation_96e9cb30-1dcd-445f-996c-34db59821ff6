import { create } from 'zustand';
import { getFeatureTitle } from '@/helpers/tier/featureTitles';

const useModalStore = create((set) => ({
  showPlans: false,
  showInlinePlans: false,
  currentFeature: null,
  currentFeatureTitle: null,
  inlineCurrentFeature: null,
  inlineCurrentFeatureTitle: null,
  setShowPlans: (show) => set({ showPlans: show }),
  setShowInlinePlans: (show) => set({ showInlinePlans: show }),
  setCurrentFeature: (feature) => set({ currentFeature: feature }),
  setInlineCurrentFeature: (feature) => set({ inlineCurrentFeature: feature }),
  openPlansModal: (feature) => set({
    showPlans: true,
    currentFeature: feature,
    currentFeatureTitle: getFeatureTitle(feature)
  }),
  openInlinePlansModal: (feature) => set({
    showInlinePlans: true,
    inlineCurrentFeature: feature,
    inlineCurrentFeatureTitle: getFeatureTitle(feature)
  }),
  closePlansModal: () => set({
    showPlans: false,
    currentFeature: null,
    currentFeatureTitle: null
  }),
  closeInlinePlansModal: () => set({
    showInlinePlans: false,
    inlineCurrentFeature: null,
    inlineCurrentFeatureTitle: null
  }),
}));

export default useModalStore; 